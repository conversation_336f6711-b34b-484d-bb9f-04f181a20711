{"name": "instructor-classes-extension", "version": "2.2.2", "description": "Description of instructor-classes-extension", "license": "Apache-2.0", "private": true, "engines": {"node": ">=20.0.0 <21"}, "repository": {"type": "git", "url": "https://github.com/ellucian-developer/experience-ethos-examples", "directory": "instructor-classes/extension"}, "scripts": {"lint": "npx eslint --ext .jsx,.js src", "build-dev": "npx webpack --progress --mode development --env verbose", "build-prod": "npx webpack --progress --mode production --env verbose", "deploy-dev": "npx webpack --progress --mode development --env verbose --env upload", "deploy-dev-force": "npx webpack --progress --mode development --env verbose --env upload --env forceUpload", "deploy-prod": "npx webpack --progress --mode production --env verbose --env upload", "watch-and-upload": "npx webpack --hot --watch --mode development --env verbose --env upload --env forceUpload", "start": "npx webpack serve --mode development --env verbose --env liveReload"}, "dependencies": {"@ellucian/ds-icons": "https://cdn.elluciancloud.com/assets/EDS2/7.17.0/umd/path_design_system_icons.tgz", "@ellucian/experience-extension-extras": "github:ellucian-developer/experience-extension-extras#1.2.0", "@ellucian/experience-extension-utils": "https://cdn.elluciancloud.com/assets/SDK/utils/1.0.0/ellucian-experience-extension-utils-1.0.0.tgz", "@ellucian/react-design-system": "https://cdn.elluciancloud.com/assets/EDS2/7.17.0/umd/path_design_system.tgz", "@tanstack/react-query": "^4.32.0", "classnames": "^2.3.2", "dotenv-webpack": "^7.1.1", "jwt-decode": "3.1.2", "loglevel": "^1.8.1", "prop-types": "15.7.2", "react": "17.0.2", "react-dom": "17.0.2", "react-intl": "5.12.5"}, "devDependencies": {"@babel/eslint-parser": "7.17.0", "@babel/plugin-transform-runtime": "7.12.1", "@babel/preset-env": "7.12.1", "@babel/preset-react": "7.12.1", "@calm/eslint-plugin-react-intl": "1.4.1", "@ellucian/experience-extension": "https://cdn.elluciancloud.com/assets/SDK/7.15.0/ellucian-experience-extension-7.15.0.tgz", "babel-plugin-rewire": "1.2.0", "cross-env": "7.0.2", "dotenv": "8.2.0", "eslint": "8.32.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-react": "^7.28.0", "webpack": "^5.93.0", "webpack-cli": "4.10.0", "webpack-dev-server": "4.15.2"}, "overrides": {"axios": "^1.8.1"}}