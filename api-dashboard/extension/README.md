# API Dashboard
## The Extension
This example includes a card that can be used to observe the other examples of API requests. This simple card is to allow you to do simple comparisons of the various examples by capturing API request times.

To upload and use this extension you will need to do the following from the api-dashboard/extension directory:

* Run 'npm install'
* Set the "publisher" in extension.js. This should be the name of your institution or organization.
* Copy sample.env to .env. Adding your upload token and uncommenting and editing the other vars as appropriate.
* Run one of the deploy scripts in package.json. Such as "watch-and-upload" or "deploy-dev".
* Use Experience Setup to enable or verify your new extension is enabled, and is associated with an Environment.

<br/>

Copyright 2021–2023 Ellucian Company L.P. and its affiliates.
