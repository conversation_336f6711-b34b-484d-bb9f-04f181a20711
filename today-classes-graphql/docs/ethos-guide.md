# Today's Classes GraphQL Proxy Ethos Guide

This Today's Classes example uses the Experience GraphQL Proxy to query Ethos GraphQL.

## Ethos GraphQL Setup

This example uses Experience's GraphQL Proxy. The API Key used to access Ethos is the key configured to be used by Experience.

Since this example uses GraphQL there are some additional steps to set up. This includes loading the needed resources into Ellucian Data Access and granting permission to use Ethos Integration GraphQL for these resources.

You can follow the Experience documentation [Set up GraphQL requests to Data Access](https://resources.elluciancloud.com/bundle/ellucian_experience_acn_configure/page/c_set_up_graphql.html).

The following resources will need to be loaded into Data Access and permission given to the Experience Ethos Integration Application for this example:

* buildings
* courses
* instructional-events
* persons
* rooms
* section-registrations
* sections
* subjects

Copyright 2021–2023 Ellucian Company L.P. and its affiliates.