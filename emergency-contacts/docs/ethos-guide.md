# Ethos Guide

This example uses Ethos Integration to create, read, update and delete SIS data. This example uses the following resources:

## Banner resources

1. EEDM person-emergency-contacts [Person Emergency Contacts: person-emergency-contacts v1.0.0](https://resources.elluciancloud.com/bundle/banner_api_ethos_api_person_emergency_contacts_1.0.0/page/person-emergency-contacts.html)
1. BP API emergency-contacts [emergency-contacts API documentation v1.1.0](https://resources.elluciancloud.com/bundle/banner_api_business_api_emergency_contacts_1.1.0/page/emergency-contacts.html)   

## Colleague resources
1. EEDM person-emergency-contacts [Person Emergency Contacts: person-emergency-contacts v1.0.0](https://resources.elluciancloud.com/bundle/colleague_api_ethos_api_person_emergency_contacts_1.0.0/page/person-emergency-contacts.html)

## Ethos Integration Setup

This example needs an Ethos Integration Application's API Key to make the API calls to Ethos Integration. You can use the Integration Application used by Experience or create another one as needed. To create a new one, you can follow the steps as outlined in the Experience documentation [Create an application in Ethos Integration for Ellucian Experience](https://resources.elluciancloud.com/bundle/ellucian_experience_acn_configure/page/t_create_app_ethos_experience.html) for the purpose of this card.

You will need to ensure the resources listed above are available on your underlying ERP APIs with the credentials used.

Copyright 2021–2023 Ellucian Company L.P. and its affiliates.
