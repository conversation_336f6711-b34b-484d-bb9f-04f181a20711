# Do not to commit this file to your source control system as it will contain sensitive secrets. If you use a CI/CD system such as <PERSON>, use its mechanism for securely managing secrets and environment variables.
# Follow the 'Quick Start' section in the readme.md
EXPERIENCE_EXTENSION_UPLOAD_TOKEN=<upload-token>

# The following are optional environment variables which can be used to setup your extension.
# View 'Utilizing the Setup API' section of the readme.md for more details.
# To utilize them, uncomment and fill out:
# EXPERIENCE_EXTENSION_ENABLED=<true/false>
# EXPERIENCE_EXTENSION_ENVIRONMENTS=<Tenant1,Tenant2> 

# Card Config
# Banner
# PIPELINE_GET_EMERGENCY_CONTACTS=ethos-example-get-emergency-contacts
# PIPELINE_POST_EMERGENCY_CONTACTS=ethos-example-post-emergency-contacts
# PIPELINE_PUT_EMERGENCY_CONTACTS=ethos-example-banner-put-emergency-contacts
# PIPELINE_DELETE_EMERGENCY_CONTACTS=ethos-example-banner-delete-emergency-contacts

# Colleague 
# PIPELINE_GET_EMERGENCY_CONTACTS=ethos-example-get-emergency-contacts
# PIPELINE_POST_EMERGENCY_CONTACTS=ethos-example-post-emergency-contacts
# PIPELINE_PUT_EMERGENCY_CONTACTS=ethos-example-colleague-put-emergency-contacts
# PIPELINE_DELETE_EMERGENCY_CONTACTS=ethos-example-colleague-delete-emergency-contacts