{"parser": "@babel/eslint-parser", "extends": ["eslint:recommended", "plugin:import/recommended", "plugin:jsx-a11y/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"babelOptions": {"presets": ["@babel/preset-react"]}, "ecmaFeatures": {"experimentalObjectRestSpread": true, "jsx": true}, "requireConfigFile": false, "sourceType": "module"}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx"]}}, "react": {"version": "detect"}, "linkComponents": ["Hyperlink", {"name": "Link", "linkAttribute": "to"}]}, "env": {"browser": true, "node": true, "es6": true}, "rules": {"no-debugger": ["warn"], "no-unused-vars": ["warn"], "indent": ["warn", 4, {"MemberExpression": "off", "SwitchCase": 1}], "linebreak-style": ["error", "unix"], "quotes": ["warn", "single"], "semi": ["warn", "always"]}}