{"name": "ethos-example-get-emergency-contacts", "parameters": [{"name": "ethosApiKey", "type": "string", "required": true, "sensitive": true}, {"name": "testPersonId", "type": "string", "default": ""}], "apiDefinition": {"authType": "userToken", "httpVerb": "GET"}, "pipeline": ["Setup user", "Get person-emergency-contacts"], "segments": {"Setup user": {"class": "JavaScriptTransform", "config": {"pushUndefined": true, "stopOnError": false, "draft": false, "code": "function transform (message, context) {\n  \n  const contextUser = context.get('__user');\n  const testPersonId = context.get('testPersonId');\n\n  const personId = contextUser?.id || testPersonId;\n\n  context.set('personId', personId);\n\n  return { payload: {} };\n}\n"}}, "Get person-emergency-contacts": {"class": "ethosProxyGetFilter", "config": {"resource": "person-emergency-contacts", "filter": "?criteria={\"person\":{\"id\":\"{{{context.personId}}}\"}}", "acceptVersions": ["1"], "cache": false, "queryByPost": false, "ignoreErrors": false}}}}