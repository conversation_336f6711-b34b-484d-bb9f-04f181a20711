{"name": "myethosutest3-example-colleague-put-emergency-contacts", "parameters": [{"name": "ethosApiKey", "type": "string", "required": true, "sensitive": true}, {"name": "testPersonId", "type": "string", "default": ""}], "apiDefinition": {"authType": "userToken", "httpVerb": "PUT"}, "pipeline": ["setup", "Get person-emergency-contact", "confirm-person-matches", "Delete existing person-emergency-contact", "build-post-body", "Post person-emergency-contact"], "segments": {"setup": {"class": "JavaScriptTransform", "config": {"pushUndefined": true, "stopOnError": false, "draft": false, "code": "function transform (message, context) {\n  const { payload: contact } = message;\n\n  const { '__user': user, testPersonId } = context;\n  const personId = user?.id || testPersonId;\n  \n  context.set('contact', contact);\n  context.set('personId', personId);\n  \n  return message;\n}\n"}}, "Get person-emergency-contact": {"class": "ethosProxyGet", "config": {"resource": "person-emergency-contacts", "acceptVersions": ["1"], "idFromContext": "$.contact.id", "target": "personEmergencyContact", "cache": false, "ignoreErrors": false}}, "confirm-person-matches": {"class": "JavaScriptTransform", "config": {"pushUndefined": true, "stopOnError": false, "draft": false, "code": "function transform (message, context) {\n  const { payload: { personEmergencyContact } = {} } = message;\n  const { personId } = context;\n  \n  if ( personEmergencyContact.person.id !== personId ) {\n    const message = 'User it not allows to update this resource record';\n    context.set('__httpStatusMessage', message);\n    context.set('__httpStatusCode', 403);\n    throw new Error(message);\n  }\n  \n  // success\n  return message;\n}"}}, "Delete existing person-emergency-contact": {"class": "ethosProxyDelete", "config": {"resource": "person-emergency-contacts", "idFromContext": "$.contact.id", "ignoreErrors": false}}, "build-post-body": {"class": "JavaScriptTransform", "config": {"pushUndefined": true, "stopOnError": false, "draft": false, "code": "function transform (message, context) {\n  const { contact } = context;\n  \n  // Zero out the ID\n  contact.id = '00000000-0000-0000-0000-000000000000';\n  \n  return {\n    payload: contact\n  };\n}"}}, "Post person-emergency-contact": {"class": "ethosProxyPost", "config": {"resource": "person-emergency-contacts", "contentVersion": "1", "acceptVersion": "1", "bodyPath": "$", "ignoreErrors": false}}}}