// Copyright 2021-2025 Ellucian Company L.P. and its affiliates.

import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useIntl } from 'react-intl';

import { Button, makeStyles, Typography, Grid, Card, CardContent } from '@ellucian/react-design-system/core'
import { colorFillAlertError, colorTextAlertSuccess, spacing30, spacing40, spacing80, spacing20 } from '@ellucian/react-design-system/core/styles/tokens';

import { withIntl } from '../i18n/ReactIntlProviderWrapper';

import { useCardControl, useCardInfo, useExtensionControl } from '@ellucian/experience-extension-utils';

import { DataQueryProvider, userTokenDataConnectQuery, useDataQuery } from '@ellucian/experience-extension-extras';
import { useDashboard } from '../hooks/dashboard';

// initialize logging for this card
import { initializeLogging } from '../util/log-level';
initializeLogging('default');

import log from 'loglevel';
const logger = log.getLogger('default');

// Dummy data for preview/testing
const dummyLeaveData = [
    {
        leavDesc: "Vacation Admin",
        taken: "5.0",
        accrued: "20.0",
        totalBalance: "15.0",
        dateAvail: "2024-01-01"
    },
    {
        leavDesc: "Sick Leave Admin",
        taken: "2.5",
        accrued: "12.0",
        totalBalance: "9.5",
        dateAvail: "2024-01-15"
    },
    {
        leavDesc: "Personal Business Admin",
        taken: "1.0",
        accrued: "8.0",
        totalBalance: "7.0",
        dateAvail: "2024-02-01"
    },
    {
        leavDesc: "Overtime in Lieu",
        taken: "0.5",
        accrued: "3.0",
        totalBalance: "2.5",
        dateAvail: "2024-02-15"
    }
];

const useStyles = makeStyles(() => ({
    root:{
        height: '100%',
        overflowY: 'auto'
    },
    content: {
        height: '100%',
        marginTop: 0,
        marginRight: spacing40,
        marginBottom: 0,
        marginLeft: spacing40,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-around'
    },
    leaveDetails: {
        float: 'right',
        marginTop: spacing40
    },
    leaveBalanceLabel: {
        marginBottom: spacing30
    },
    leaveGrid: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: spacing20,
        marginTop: spacing30
    },
    leaveCard: {
        padding: spacing20,
        textAlign: 'center',
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        backgroundColor: '#fafafa'
    },
    leaveType: {
        marginBottom: spacing20,
        fontWeight: 'bold'
    },
    leaveBalance: {
        fontSize: '1.5rem',
        fontWeight: 'bold',
        color: colorTextAlertSuccess
    },
    transactionAmountPayment: {
        color: colorTextAlertSuccess
    },
    message: {
        marginLeft: spacing80,
        marginRight: spacing80,
        textAlign: 'center'
    }
}), { index: 2});

function LeaveBalance({ hasPipelineApi = true }) {
    const intl = useIntl();
    const classes = useStyles();

    // Experience SDK hooks
    const { navigateToPage } = useCardControl();
    const { setErrorMessage, setLoadingStatus } = useExtensionControl();
    const {
        configuration: {
            pipelineApi
        } = {}
    } = useCardInfo();

    // Only use data query if pipeline API is available
    let data, dataError, inPreviewMode, isError, isLoading, isRefreshing;

    if (hasPipelineApi) {
        const dataQueryResult = useDataQuery(pipelineApi);
        ({ data, dataError, inPreviewMode, isError, isLoading, isRefreshing } = dataQueryResult);
        useDashboard();
    } else {
        // Set default values when no pipeline API is available
        data = null;
        dataError = null;
        inPreviewMode = true;
        isError = false;
        isLoading = false;
        isRefreshing = false;
    }

    const [ leaves, setLeaves ] = useState();

    useEffect(() => {
        setLoadingStatus(isRefreshing || (!data && isLoading));
    }, [data, isLoading, isRefreshing])

    useEffect(() => {
        if (data && Array.isArray(data)) {
            setLeaves(data?.sort((left, right) => (right.dateAvail?.localeCompare(left.dateAvail))));
        } else if (!hasPipelineApi || inPreviewMode || !data) {
            // Use dummy data when no pipeline API is configured, in preview mode, or when no real data is available
            setLeaves(dummyLeaveData?.sort((left, right) => (right.dateAvail?.localeCompare(left.dateAvail))));
        }
    }, [data, inPreviewMode, hasPipelineApi, dummyLeaveData])

    useEffect(() => {
        if (isError) {
            setErrorMessage({
                headerMessage: intl.formatMessage({id: 'LeaveBalance.contactAdministrator'}),
                textMessage: intl.formatMessage({id: 'LeaveBalance.dataError'}),
                iconName: 'warning',
                iconColor: colorFillAlertError
            });
        }
    }, [isError, setErrorMessage])

    const onLeaveDetailsClick = useCallback(() => {
        // open the page
        navigateToPage({route: '/'});
    }, [navigateToPage])

    if (!data && inPreviewMode && dataError?.statusCode === 404 && hasPipelineApi) {
        return (
            <div className={classes.root}>
                <div className={classes.content}>
                    <Typography className={classes.message} variant="body1" component="div">
                        {intl.formatMessage({ id: 'LeaveBalance.notConfigured'})}
                    </Typography>
                </div>
            </div>
        );
    } else if (leaves && Array.isArray(leaves) && leaves.length > 0) {
        return (
            <div className={classes.root}>
            <div className={classes.content}>
                <>
                    {Array.isArray(leaves) && leaves.length > 0 && (
                        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                        <div>
                            <Typography variant={'h4'} component={'div'} className={classes.leaveBalanceLabel}>
                                {intl.formatMessage({id: 'LeaveBalance.leaveBalance'})}
                            </Typography>
                            <div className={classes.leaveGrid}>
                                {leaves.map((leave, index) => {
                                    const { leavDesc, totalBalance } = leave;
                                    return (
                                        <div key={index} className={classes.leaveCard}>
                                            <Typography variant={'body2'} component={'div'} className={classes.leaveType}>
                                                {leavDesc}
                                            </Typography>
                                            <Typography variant={'h3'} component={'div'} className={classes.leaveBalance}>
                                                {totalBalance}
                                            </Typography>
                                        </div>
                                    );
                                })}
                            </div>
                            <Button className={classes.leaveDetails} color='secondary' onClick={onLeaveDetailsClick}>
                                {intl.formatMessage({id: 'LeaveBalance.details'})}
                            </Button>
                        </div>
                    )}
                </>
            </div>
            </div>
        );
    } else {
        return (
            <div className={classes.root}>
                <div className={classes.content}>
                    <Typography className={classes.message} variant="body1" component="div">
                        {intl.formatMessage({ id: 'LeaveBalance.noLeave'})}
                    </Typography>
                </div>
            </div>
        );
    }

}

LeaveBalance.propTypes = {
    hasPipelineApi: PropTypes.bool
};

function LeaveBalanceWithProviders() {
    const {
        configuration: {
            pipelineApi
        } = {}
     } = useCardInfo();

     // If pipelineApi is not configured, render component without DataQueryProvider
     if (!pipelineApi || pipelineApi === '') {
        logger.info('"pipelineApi" is not configured. Using dummy data.');
        return <LeaveBalance hasPipelineApi={false} />;
    }

    const options = {
        queryFunction: userTokenDataConnectQuery,
        resource: pipelineApi
    }

    return (
        <DataQueryProvider options={options}>
            <LeaveBalance hasPipelineApi={true} />
        </DataQueryProvider>
    )
}

export default withIntl(LeaveBalanceWithProviders);