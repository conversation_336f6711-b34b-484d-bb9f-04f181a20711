# Do not to commit this file to your source control system as it will contain sensitive secrets. If you use a CI/CD system such as <PERSON>, use its mechanism for securely managing secrets and environment variables.
# Follow the 'Quick Start' section in the readme.md
EXPERIENCE_EXTENSION_UPLOAD_TOKEN=<upload-token>

# The following are optional environment variables which can be used to setup your extension.
# View 'Utilizing the Setup API' section of the readme.md for more details.
# To utilize them, uncomment and fill out:
# EXPERIENCE_EXTENSION_SHARED_SECRET=<shared-secret>
# EXPERIENCE_EXTENSION_ENABLED=<true/false>
# EXPERIENCE_EXTENSION_ENVIRONMENTS=<Tenant1,Tenant2> 