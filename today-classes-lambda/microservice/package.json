{"name": "today-classes-lambda", "version": "2.0.3", "description": "Provides an Experience authorized endpoint to retrieve Today's Classes with a lambda function", "license": "Apache-2.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.3.0"}, "repository": {"type": "git", "url": "https://github.com/ellucian-developer/experience-ethos-examples", "directory": "today-classes-lambda/microservice"}, "scripts": {"start": "npx serverless offline --stage dev", "deploy-dev": "npx serverless deploy --stage dev"}, "dependencies": {"@ellucian/experience-extension-server-util": "github:ellucian-developer/experience-extension-server-util#1.0.6", "@middy/core": "^4.1.0", "@middy/http-error-handler": "^4.1.0", "@middy/http-header-normalizer": "^4.1.0", "http-status-codes": "^2.2.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.25.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "serverless": "^3.30.1", "serverless-bundle": "^6.0.0", "serverless-deployment-bucket": "^1.6.0", "serverless-offline": "^12.0.4", "serverless-provisioned-concurrency-autoscaling": "^1.9.1"}, "overrides": {"webpack": "^5.82.1"}}