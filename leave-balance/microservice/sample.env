ENVIRONMENT=dev
REGION=us-east-1
DEPLOYMENT_BUCKET=microservice-deployments
TAG_NAME_BASE="today-classes"
TAG_GROUP=your-group-name
TAG_COST_CENTER=you-may-not-need-this-if-not-remove-tag-from-serverless-yml
TAG_PRODUCT=today-classes
TAG_POC=point-of-contact-email

ETHOS_INTEGRATION_URL=https://integrate.elluciancloud.com
JWT_SECRET=your-experience-extension-shared-secret
EXTENSION_API_TOKEN=your-experience-extension-api-token

# Optional to use LOG_LEVEL to specify the loglevel to use for logging
#LOG_LEVEL=debug